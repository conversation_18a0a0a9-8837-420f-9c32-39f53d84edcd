package com.example;

import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;


@RestController
public class LoginController {

    @GetMapping("/")
    public String index(Model model, @AuthenticationPrincipal OAuth2User user) {
        model.addAttribute("user", user != null ? user.getAttributes() : null);
        return "index";
    }

    @GetMapping("/public")
    @ResponseBody
    public String publicPage() {
        return "這是公開頁面";
    }
}
