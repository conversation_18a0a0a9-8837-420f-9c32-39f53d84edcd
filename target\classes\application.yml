server:
  port: 8081

spring:
  mvc:
    view:
      prefix: /WEB-INF/views/
      suffix: .jsp

  security:
    oauth2:
      client:
        registration:
          demo-client:
            client-id: demo-client
            client-secret: demo-secret
            authorization-grant-type: authorization_code
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
            scope: read
        provider:
          demo-client:
            authorization-uri: http://localhost:9000/oauth2/authorize
            token-uri: http://localhost:9000/oauth2/token
            user-info-uri: http://localhost:9000/userinfo
